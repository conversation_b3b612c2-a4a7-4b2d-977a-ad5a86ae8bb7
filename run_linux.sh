#export CMAKE_COMMAND_COMMON="$CMAKE_COMMAND_COMMON -DBUILD_SHARED_LIBS=on"
#export CMAKE_COMMAND_COMMON="$CMAKE_COMMAND_COMMON -DBUILD_MULTI_API=on -DBUILD_APPS=on"
#export CMAKE_COMMAND_COMMON="$CMAKE_COMMAND_COMMON -DBUILD_SHARED_LIBS=off -DSTATIC_LINK_PLUGINS=off"

export CONAN_USER_REQUIRES="['leopard/jenkins#a01a99b64bfa14f5d833eadf2cd37f885a95eba8','xr_codec/jenkins#b06b81bb2a5bd7f404cb183614a857dbca7c8435','nr_display/jenkins#671c1bb0d4327f786f31005b387e99b3015d8c88','dispinterrupt/jenkins#0174f8b0679a67eaa3250cf9ea8d53d9b71df288','ov580_driver/jenkins#dcc9de17eeb72ec5c2e88a62616dc86138df69af','framework/jenkins#353210b961468d3828d5f2a166a0ae3158b53744','cocoaengine/jenkins#80d2af6ba525612801d57d3948f2a54b284c9386','perception_sdk_sequoia/jenkins#11e56e3cda9a5449c32038bd5695669cfb5cbb14','genthreedof/jenkins#78d03fe8c5f687f4343b2cc6232346e4c5eb94ff','nrslam-old/jenkins#69492ebbb6fac87ac5e6d08e7e9e35b198f65f55','chameleon/jenkins#729097226246e1cb06e6998b30435251e15b73cb','warpcore/jenkins#d1c4b0fbf40bb9e5cadf7f1dae421a10bdeedc9a','sparrow/jenkins#468c7dbe588d66216a578292dd46760fe7000858','xr_timesync/jenkins#4d70e474faa0772a94993b979f02dc85d3db4198','nrealutil/jenkins#44bf9e7a2c66fdce0be78b3e8a3bb2552ff5adc1']"

./compile.sh native app
