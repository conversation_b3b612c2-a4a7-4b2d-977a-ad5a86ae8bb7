#pragma once
#include <libusb.h>
#include <functional>
#include <vector>
#include <memory>
#include <atomic>
#include <common.h>
#include <data_types.h>
#include "file_writer.h"


struct UrbAssemblyState{
    uint64_t bytes_remaining{0};
    bool has_parse_header{false};

};

#define USB_NUM_TRANSFER_BUFS 60 
#define TRANSFER_BUF_SIZE	( 4 * 1024 ) //16KB


#define IMU_FRAME_BUF_SIZE	( 16 * 1024 )

#define GRAY_FRAME_BUF_SIZE	( 4 * 1024 * 1024 ) 

#define RGB_FRAME_BUF_SIZE	( 4 * 1024 * 1024) 


class AsyncUSBDevice;

//1个in端点对应1个TransferContext
struct TransferContext {
    uint32_t ep_index;
    EndpointInfo end_point_info;
    struct libusb_transfer *transfers[USB_NUM_TRANSFER_BUFS];
    uint8_t *transfer_bufs[USB_NUM_TRANSFER_BUFS];
    AsyncUSBDevice* async_usb_device;

    std::vector<uint8_t> outbuf;
    uint32_t outbuf_real_size{0};

    std::vector<uint8_t> holdbuf;
    uint32_t holdbuf_real_size{0};

    std::vector<uint8_t> leftover_buf;
    uint32_t leftover_buf_real_size{0};
    bool has_parsed_header{false};
    FrameHeader frame_header{0,0};

    uint64_t packet_count{0};
    uint64_t last_print_fps_time{0}; //单位ns
};

struct DataFrame{
    FrameType type{None};
    std::vector<uint8_t> data;
    uint32_t data_size{0};
};

class AsyncUSBDevice {
public:
    
    AsyncUSBDevice();
    ~AsyncUSBDevice();
    
    bool open();
    void close();
    
    void HandleEvents();

    bool FindUsbDevice(const std::string& interface_description);
    libusb_device_handle *  GetInterfaceString(libusb_device *dev, int interface_index,std::string& interface_string);
    bool OpenDevice();

    void ProcessPayload(TransferContext* transfer_context, uint8_t* data, int data_length, int all_length);

    std::atomic<bool> event_thread_running_{false};

    FrameType GetFrameTypeByEpIndex(uint32_t ep_index);

    void PublishGrayFrame(TransferContext* transfer_context);

    void PublishImuFrame(TransferContext* transfer_context); 

    void PublishRgbFrame(TransferContext* transfer_context); 

    void TraceFrameRate(uint64_t current_time_ns, const std::string& tag, TransferContext* transfer_context);

private:

    void SwapBuffers(TransferContext* transfer_context);

    void SaveDataFrame(const DataFrame& data_frame);

    static void LIBUSB_CALL transfer_callback(libusb_transfer* transfer);
    
    libusb_device_handle* device_handle_{nullptr};
    std::vector<InterfaceInfo> interface_infos_;

    std::vector<TransferContext>  transfer_contexts_;
    uint32_t in_ep_count_{0};
    uint32_t out_ep_count_{0};

    std::mutex mutex_;
    std::condition_variable cond_;

    std::shared_ptr<std::thread> gray_work_thread_;
    std::mutex gray_mutex_;
    std::condition_variable gray_cond_;
    DataFrame gray_frame_;
    FileWriter gray_file_writer_;

    std::shared_ptr<std::thread> imu_work_thread_;
    std::mutex imu_mutex_;
    std::condition_variable imu_cond_;
    DataFrame imu_frame_;

    std::shared_ptr<std::thread> rgb_work_thread_;
    std::mutex rgb_mutex_;
    std::condition_variable rgb_cond_;
    DataFrame rgb_frame_;
    FileWriter rgb_file_writer_;

    libusb_context *usb_ctx_{nullptr};


    std::shared_ptr<std::thread> handle_event_thread_;
    std::string interface_description_;

};