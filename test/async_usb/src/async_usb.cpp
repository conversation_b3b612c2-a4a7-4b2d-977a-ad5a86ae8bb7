#include "async_usb.h"
#include <thread>
#include <iostream>
#include <fstream>
#include <framework/util/os_time.h>

#if defined(TEST_SYSTEM_ANDROID) || defined(TEST_SYSTEM_LINUX)
#include <unistd.h>
#include <sys/syscall.h>
#include <sched.h>
#endif


using namespace framework::util;

AsyncUSBDevice::AsyncUSBDevice():gray_file_writer_("graycamera_data.bin"),rgb_file_writer_("rgb_data.bin"){
    if(libusb_init(&usb_ctx_) != 0){
        LOG_ERROR("libusb_init failed");
    }
    else{
        libusb_set_option(usb_ctx_, LIBUSB_OPTION_LOG_LEVEL, LIBUSB_LOG_LEVEL_INFO);
    }
}

AsyncUSBDevice::~AsyncUSBDevice() {
    close();
}

libusb_device_handle * AsyncUSBDevice::GetInterfaceString(libusb_device *dev, int iInterface,std::string& interface_string) {
    libusb_device_handle *handle = nullptr;
    char buffer[256] = {0};
    if (libusb_open(dev, &handle) != 0) {
        LOG_ERROR("GetInterfaceString Failed to open device");
        return nullptr;
    }
    int r = libusb_get_string_descriptor_ascii(handle, iInterface, (unsigned char*)buffer, sizeof(buffer));
    if (r > 0) {
        interface_string = std::string(buffer);
        LOG_INFO("GetInterfaceString interface string: {}",buffer);
    } else {
        LOG_WARN("GetInterfaceString Failed to get iInterface string");
    }
    //libusb_close(handle);
    return handle;
}


bool AsyncUSBDevice::FindUsbDevice(const std::string& interface_description) {
    interface_description_ = interface_description;
    libusb_device** devs = nullptr;
    ssize_t cnt = libusb_get_device_list(usb_ctx_, &devs);
    interface_infos_.clear();

    bool found = false;

    for (ssize_t i = 0; i < cnt; ++i) {
        libusb_device* dev = devs[i];
        libusb_device_descriptor desc;

        if (libusb_get_device_descriptor(dev, &desc) != 0) continue;

        libusb_config_descriptor* config = nullptr;
        if (libusb_get_config_descriptor(dev, 0, &config) != 0) continue;

        for (int i = 0; i < config->bNumInterfaces; ++i) {
            const libusb_interface& iface = config->interface[i];

            for (int j = 0; j < iface.num_altsetting; ++j) {
                const libusb_interface_descriptor& iface_desc = iface.altsetting[j];

                InterfaceInfo interfaceInfo;
                interfaceInfo.interfaceNumber = iface_desc.bInterfaceNumber;
                std::string interface_string;
                libusb_device_handle* handle = GetInterfaceString(dev,iface_desc.iInterface,interface_string);
                if(handle != nullptr && interface_string == interface_description_){
                    device_handle_ = handle;
                    found = true;
                    LOG_INFO("Find frames interface:{}",interface_description_);
                    for (int k = 0; k < iface_desc.bNumEndpoints; ++k) {
                        const libusb_endpoint_descriptor& ep = iface_desc.endpoint[k];
                        interfaceInfo.endpoints.push_back({
                            ep.bEndpointAddress,
                            ep.bmAttributes,
                            ep.wMaxPacketSize,
                            ep.bInterval
                        });
                        if(is_in_endpoint(ep.bEndpointAddress)){
                            in_ep_count_++;
                        }
                        else if(is_out_endpoint(ep.bEndpointAddress)){
                            out_ep_count_++;
                        }
                    }
                    interface_infos_.push_back(interfaceInfo);
                    break;
                }
                else if(handle != nullptr){
                    libusb_close(handle);
                }
            }
            if(found){
                break;
            }
        }
        libusb_free_config_descriptor(config);
        if(found){
            break;
        }
    }

    libusb_free_device_list(devs, 1);

    if(found){
        for (const auto& iface : interface_infos_) {
            std::cout << "Interface: " << iface.interfaceNumber << "\n";
            for (const auto& ep : iface.endpoints) {
                std::cout << "  Endpoint: 0x" << std::hex << (int)ep.address
                        << ", Attr: 0x" << (int)ep.attributes
                        << ", MaxPktSize: " << std::dec << ep.maxPacketSize
                        << ", Interval: " << (int)ep.interval;
                if(is_in_endpoint(ep.address)){
                    std::cout << ", Direction: in";
                }
                else if(is_out_endpoint(ep.address)){
                    std::cout << ", Direction: out";
                }
                else{
                    std::cout << ", Direction: unknown";
                }

                switch (get_transfer_type(ep))
                {
                case CONTROL:
                    std::cout << ", Type: control";
                    break;
                case ISOCHRONOUS:
                    std::cout << ", Type: isochronous";
                    break;
                case BULK:
                    std::cout << ", Type: bulk";
                    break;
                case INTERRUPT:
                    std::cout << ", Type: interrupt";
                    break;
                default:
                    std::cout << ", Type: unknown";
                    break;
                }
                std::cout << "\n";
            }
        }
    }
    else{
        LOG_ERROR("Can not find interface:{}",interface_description_);
    }
    return found;
}


bool AsyncUSBDevice::OpenDevice() {
    int ret = libusb_detach_kernel_driver(device_handle_, interface_infos_[0].interfaceNumber); //在mac上，需要sudo执行，才能detach成功，否则会返回没有权限的错误（LIBUSB_ERROR_ACCESS）。
    LOG_TRACE("libusb_detach_kernel_driver ret {}", ret);

    if (ret == LIBUSB_SUCCESS || ret == LIBUSB_ERROR_NOT_FOUND || ret == LIBUSB_ERROR_NOT_SUPPORTED) {
        ret = libusb_claim_interface(device_handle_, interface_infos_[0].interfaceNumber);
        if ( ret != LIBUSB_SUCCESS) {
            LOG_TRACE("libusb_claim_interface ret {}", ret);
            libusb_close(device_handle_);
            device_handle_ = nullptr;
        }
        else{
            LOG_INFO("OpenDevice: claim interface {} success", interface_infos_[0].interfaceNumber);
            return true;
        }
    } else {
        LOG_TRACE("not claiming interface {}: unable to detach kernel driver ({})", interface_infos_[0].interfaceNumber, ret);
    }
    return false;
}


FrameType AsyncUSBDevice::GetFrameTypeByEpIndex(uint32_t ep_index){

#if USE_NEW_USB
    if(interface_description_ == FRAMES_INTERFACE_STRING_CAMERA){
        if(ep_index == 0){
            return FrameType::GraycameraFromFrames;
        }
        else if(ep_index == 2) {
            return FrameType::RgbFromFrames;
        }
    }
    else if(interface_description_ == FRAMES_INTERFACE_STRING_IMU){
        if(ep_index == 0){
            return FrameType::ImuFromFrames;
        }
    }
 #else
    if(interface_description_ == FRAMES_INTERFACE_STRING){
        if(ep_index == 0){
            return FrameType::GraycameraFromFrames;
        }
        else if(ep_index == 2) {
            return FrameType::RgbFromFrames;
        }
        else if(ep_index == 4){
            return FrameType::ImuFromFrames;
        }
    }
 #endif

    return FrameType::None;
}

void AsyncUSBDevice::PublishImuFrame(TransferContext* transfer_context){
    if (!event_thread_running_) return;
}




void AsyncUSBDevice::PublishGrayFrame(TransferContext* transfer_context){
    while(event_thread_running_) {
        {
            std::unique_lock<std::mutex> lock(gray_mutex_);
            LOG_TRACE("PublishGrayFrame wait");

            gray_cond_.wait(lock);

            LOG_TRACE("PublishGrayFrame wait middle");

            if (!event_thread_running_) break;

            LOG_TRACE("PublishGrayFrame wait end");

            gray_frame_.data_size = transfer_context->holdbuf_real_size;
            memcpy(gray_frame_.data.data(), transfer_context->holdbuf.data(), transfer_context->holdbuf_real_size);
        }
        SaveDataFrame(gray_frame_);
    }
    LOG_INFO("PublishGrayFrame thread exit");
}


void AsyncUSBDevice::PublishRgbFrame(TransferContext* transfer_context){
    while(event_thread_running_) {
        {
            std::unique_lock<std::mutex> lock(rgb_mutex_);
            rgb_cond_.wait(lock);
            if (!event_thread_running_) break;
            rgb_frame_.data_size = transfer_context->holdbuf_real_size;
            memcpy(rgb_frame_.data.data(), transfer_context->holdbuf.data(), transfer_context->holdbuf_real_size);
        }
        SaveDataFrame(rgb_frame_);
    }
    LOG_INFO("PublishRgbFrame thread exit");
} 


void AsyncUSBDevice::SaveDataFrame(const DataFrame& data_frame){
    FrameHeader* frame_header = (FrameHeader*)data_frame.data.data();
    if(data_frame.type == FrameType::GraycameraFromFrames) {
        NRGrayscaleCameraFrameData* gray_frame_data = (NRGrayscaleCameraFrameData*)(data_frame.data.data() + sizeof(FrameHeader));
        uint32_t data_offset = sizeof(FrameHeader) + sizeof(NRGrayscaleCameraFrameData);
        int32_t write_size =  data_frame.data_size - data_offset;
        if(write_size > 0) {
            gray_file_writer_.Write(data_frame.data.data() + data_offset,write_size);
            LOG_TRACE("SaveDataFrame: gray frame data size:{}",write_size);
        }
        else{
            LOG_WARN("SaveDataFrame: gray frame data size is too small,data_size:{},data_offset:{},frame_header:type:{},len:{}",
                data_frame.data_size,data_offset,frame_header->type,frame_header->len);
        }
    }
    else if(data_frame.type == FrameType::RgbFromFrames) {
        uint32_t data_offset = sizeof(FrameHeader) + sizeof(NRRgbCameraFrameData);
        int32_t write_size =  data_frame.data_size - data_offset;
        if(write_size > 0) {
            rgb_file_writer_.Write(data_frame.data.data() + data_offset,write_size);
            LOG_TRACE("SaveDataFrame: rgb frame data size:{}",write_size);
        }
        else{
            LOG_WARN("SaveDataFrame: rgb frame data size is too small");
        }
    }
    else{
        LOG_WARN("SaveDataFrame: unknown frame type:{}",data_frame.type);
    }
}



void AsyncUSBDevice::SwapBuffers(TransferContext* transfer_context){
    FrameType frame_type = GetFrameTypeByEpIndex(transfer_context->ep_index) ;
    if(frame_type == FrameType::GraycameraFromFrames) {
        std::unique_lock lock(gray_mutex_);
        transfer_context->outbuf.swap(transfer_context->holdbuf);
        transfer_context->holdbuf_real_size = transfer_context->outbuf_real_size;
        LOG_TRACE("SwapBuffers: gray frame");
        gray_cond_.notify_one();
    }
    else if(frame_type == FrameType::RgbFromFrames) {
        std::unique_lock lock(rgb_mutex_);
        transfer_context->outbuf.swap(transfer_context->holdbuf);
        transfer_context->holdbuf_real_size = transfer_context->outbuf_real_size;
        rgb_cond_.notify_one();
    }
    else{
        LOG_WARN("SwapBuffers: unknown frame type",frame_type);
    }
}


void AsyncUSBDevice::TraceFrameRate(uint64_t current_time_ns, const std::string& tag, TransferContext* transfer_context){
    transfer_context->packet_count++;
    if(transfer_context->last_print_fps_time == 0) {
        transfer_context->last_print_fps_time = current_time_ns;
        return;
    }

    uint64_t ns = current_time_ns - transfer_context->last_print_fps_time;
    if (ns >= OS_NS_PER_SEC)
    {
        uint64_t fps = transfer_context->packet_count * OS_NS_PER_SEC / ns;
        LOG_INFO("{} fps: {}", tag, fps);
        transfer_context->packet_count = 0;
        transfer_context->last_print_fps_time = current_time_ns;
    }
}



bool AsyncUSBDevice::open() {
    if (!OpenDevice()) return false;
    
    event_thread_running_ = true;
 
    handle_event_thread_.reset(new std::thread(std::bind(&AsyncUSBDevice::HandleEvents, this)));

    transfer_contexts_.resize(in_ep_count_);

    uint32_t ep_index = 0;
    uint32_t in_ep_index = 0;
    for(const auto& ep : interface_infos_[0].endpoints){
        if(is_in_endpoint(ep.address)){
            TransferContext* cur_context = &(transfer_contexts_[in_ep_index]);
            cur_context->ep_index = ep_index;
            cur_context->end_point_info = ep;  
            cur_context->async_usb_device = this;
            FrameType frame_type = GetFrameTypeByEpIndex(ep_index);
            if(frame_type == ImuFromFrames) {
                cur_context->outbuf.resize(IMU_FRAME_BUF_SIZE);
                cur_context->holdbuf.resize(IMU_FRAME_BUF_SIZE);
// #if NEED_PUBLISH_FRAME
//                 imu_frame_.type = FrameType::ImuFromFrames;
//                 imu_frame_.data.resize(IMU_FRAME_BUF_SIZE);
//                 imu_frame_.data_size = 0;
//                 imu_work_thread_.reset(new std::thread(std::bind(&AsyncUSBDevice::PublishImuFrame, this, cur_context)));
// #endif
            }
            else if(frame_type == GraycameraFromFrames){
                cur_context->outbuf.resize(GRAY_FRAME_BUF_SIZE);
                cur_context->holdbuf.resize(GRAY_FRAME_BUF_SIZE);

#if NEED_PUBLISH_FRAME
                gray_frame_.type = FrameType::GraycameraFromFrames;
                gray_frame_.data.resize(GRAY_FRAME_BUF_SIZE);
                gray_frame_.data_size = 0;
                gray_work_thread_.reset(new std::thread(std::bind(&AsyncUSBDevice::PublishGrayFrame, this, cur_context)));
#endif
            }
            else if(frame_type == RgbFromFrames) {
                cur_context->outbuf.resize(RGB_FRAME_BUF_SIZE);
                cur_context->holdbuf.resize(RGB_FRAME_BUF_SIZE);

#if NEED_PUBLISH_FRAME
                rgb_frame_.type = FrameType::RgbFromFrames;
                rgb_frame_.data.resize(RGB_FRAME_BUF_SIZE);
                rgb_frame_.data_size = 0;
                rgb_work_thread_.reset(new std::thread(std::bind(&AsyncUSBDevice::PublishRgbFrame, this, cur_context)));
#endif
            }
            else{
                LOG_ERROR("AsyncUSBDevice::open Unknown frame type: {}", frame_type);
            }
            cur_context->leftover_buf.resize(TRANSFER_BUF_SIZE);

            for (int transfer_id = 0; transfer_id < USB_NUM_TRANSFER_BUFS; ++transfer_id) {
                libusb_transfer *transfer = libusb_alloc_transfer(0);
                cur_context->transfers[transfer_id] = transfer;
                cur_context->transfer_bufs[transfer_id] = (uint8_t *)malloc (TRANSFER_BUF_SIZE);
                LOG_TRACE("alloc transfer: 0x{:p}", (void *)transfer);
                if(get_transfer_type(ep) == BULK){
                    LOG_TRACE("transfer type: bulk");
                    libusb_fill_bulk_transfer ( transfer, device_handle_,
                        ep.address,
                        cur_context->transfer_bufs[transfer_id],
                        TRANSFER_BUF_SIZE, transfer_callback,
                        ( void* ) cur_context, 5000 ); 
                }
                else if(get_transfer_type(ep) == INTERRUPT){
                    LOG_TRACE("transfer type: interrupt");
                    libusb_fill_interrupt_transfer ( transfer, device_handle_,
                        ep.address,
                        cur_context->transfer_bufs[transfer_id],
                        TRANSFER_BUF_SIZE, transfer_callback,
                        ( void* ) cur_context, 5000 ); 
                }
                
            }
            in_ep_index++; 
        }
        ep_index++;
    }

    int ret = 0;
    for(auto& context : transfer_contexts_){
        int i = 0;
        for(i = 0; i < USB_NUM_TRANSFER_BUFS; ++i){
            if(context.transfers[i] != nullptr){
                LOG_TRACE("submit transfer: 0x{:p}", (void *)context.transfers[i]);
                ret = libusb_submit_transfer(context.transfers[i]);
                if(ret != LIBUSB_SUCCESS){
                    LOG_ERROR("libusb_submit_transfer error {}", ret);
                    break;
                }
            }
        }
        if(ret != LIBUSB_SUCCESS && i > 0){
            for(;i< USB_NUM_TRANSFER_BUFS; i++) {
                free(context.transfer_bufs[i]);
                libusb_free_transfer(context.transfers[i]);
                context.transfer_bufs[i] = nullptr;
                context.transfers[i] = nullptr;
            }
        }
    }






    
    return true;
}

void AsyncUSBDevice::close() {

    gray_cond_.notify_all();
    imu_cond_.notify_all();
    rgb_cond_.notify_all();

    event_thread_running_ = false;

    //wait for thread exit==》TODO

    if (!device_handle_) return;
    for(auto& context : transfer_contexts_){
        for(int i = 0; i < USB_NUM_TRANSFER_BUFS; ++i){
            if(context.transfers[i] != nullptr){
                libusb_free_transfer(context.transfers[i]);
            }
            if(context.transfer_bufs[i] != nullptr){
                free(context.transfer_bufs[i]);
            }
        }
    }
    libusb_release_interface(device_handle_, interface_infos_[0].interfaceNumber);
    /* Reattach any kernel drivers that were disabled when we claimed this interface */
    libusb_attach_kernel_driver(device_handle_, interface_infos_[0].interfaceNumber);
    libusb_close(device_handle_);
    device_handle_ = nullptr;
    libusb_exit(NULL);
}



void AsyncUSBDevice::ProcessPayload(TransferContext* transfer_context, uint8_t* data, int data_length, int all_length){

#if defined(TEST_SYSTEM_ANDROID) || defined(TEST_SYSTEM_LINUX)
    if(GetFrameTypeByEpIndex(transfer_context->ep_index) == FrameType::ImuFromFrames) {
        //提imu收线程的优先级到RR
        static bool has_set_priority = false;
        if(!has_set_priority){
            has_set_priority = true;
            pid_t tid = syscall(SYS_gettid); 
            struct sched_param param;
            param.sched_priority = 99;
            if (sched_setscheduler(0, SCHED_RR, &param) == -1) {
                LOG_INFO("sched_setscheduler error:tid:{}",tid);
            }
            else{
                LOG_INFO("sched_setscheduler success:tid:{}",tid);
            }
        }
    }
#endif
    


    // if(GetFrameTypeByEpIndex(transfer_context->ep_index) != FrameType::ImuFromFrames) {
    //     return;
    // }

    if(transfer_context->leftover_buf_real_size > 0){
        memcpy(transfer_context->outbuf.data()+transfer_context->outbuf_real_size, transfer_context->leftover_buf.data(), transfer_context->leftover_buf_real_size);
        transfer_context->outbuf_real_size += transfer_context->leftover_buf_real_size;
        transfer_context->leftover_buf_real_size = 0;
    }
    memcpy(transfer_context->outbuf.data()+transfer_context->outbuf_real_size, data, data_length);
    transfer_context->outbuf_real_size += data_length;

    if(!transfer_context->has_parsed_header) {
        if(transfer_context->outbuf_real_size >= sizeof(FrameHeader)){
            transfer_context->frame_header = *((FrameHeader*)transfer_context->outbuf.data());
            //LOG_TRACE("ProcessPayload: frame_header: {},{}", transfer_context->frame_header.len, transfer_context->frame_header.type);
            transfer_context->has_parsed_header = true;
        }
    }

    if(transfer_context->has_parsed_header) {
        uint32_t frame_total_len = transfer_context->frame_header.len + sizeof(FrameHeader);
        if(transfer_context->outbuf_real_size >= frame_total_len){
            uint64_t current_host_time_ns = FMonotonicGetNs();
            if(transfer_context->frame_header.type == FrameType::ImuFromFrames){
                ImuRawData* imu_data = (ImuRawData*)(transfer_context->outbuf.data()+sizeof(FrameHeader));
                TraceFrameRate(current_host_time_ns,"Imu", transfer_context);
                LOG_TRACE("ImuFrame: current_host_time_ns:{},{}",current_host_time_ns, *imu_data);
            }
            else if(transfer_context->frame_header.type == FrameType::GraycameraFromFrames){
                NRGrayscaleCameraFrameData* gray_frame = (NRGrayscaleCameraFrameData*)(transfer_context->outbuf.data()+sizeof(FrameHeader));
                TraceFrameRate(current_host_time_ns,"Gray", transfer_context);
                LOG_TRACE("GrayFrame: current_host_time_ns:{},{}",current_host_time_ns, *gray_frame);

#ifdef NEED_PUBLISH_FRAME
                if(gray_frame->frame_id % 60 == 0) {
                    SwapBuffers(transfer_context);
                }
#endif // NEED_PUBLISH_FRAME
            }
            else if(transfer_context->frame_header.type == FrameType::RgbFromFrames){
                NRRgbCameraFrameData* rgb_frame = (NRRgbCameraFrameData*)(transfer_context->outbuf.data()+sizeof(FrameHeader));
                TraceFrameRate(current_host_time_ns,"Rgb", transfer_context);
                LOG_TRACE("RgbFrame: current_host_time_ns:{},{}",current_host_time_ns, *rgb_frame);
#ifdef NEED_PUBLISH_FRAME
                SwapBuffers(transfer_context);
#endif // NEED_PUBLISH_FRAME
            }
            else{
                LOG_ERROR("AsyncUSBDevice::ProcessPayload Unknown frame type: {}", transfer_context->frame_header.type);
            }
            uint32_t leftover_size  = transfer_context->outbuf_real_size - frame_total_len;
            if(leftover_size > 0){
                memcpy(transfer_context->leftover_buf.data(), transfer_context->outbuf.data()+frame_total_len, leftover_size);
                transfer_context->leftover_buf_real_size = leftover_size;
            }
            else{
                transfer_context->leftover_buf_real_size = 0;
            }

            //reset
            transfer_context->outbuf_real_size = 0;
            transfer_context->has_parsed_header = false;
            transfer_context->frame_header.type = 0;
            transfer_context->frame_header.len = 0;
        }
    }
}

void LIBUSB_CALL AsyncUSBDevice::transfer_callback(libusb_transfer* transfer) {

    //LOG_TRACE("AsyncUSBDevice::transfer_callback");

    TransferContext* context = static_cast<TransferContext*>(transfer->user_data);
    int resubmit = 1;

    switch (transfer->status) {
    case LIBUSB_TRANSFER_COMPLETED: {
            if (transfer->num_iso_packets == 0) {
                context->async_usb_device->ProcessPayload(context, transfer->buffer, transfer->actual_length, transfer->length);
            } else {
                LOG_ERROR("transfer_callback num_iso_packets {} ", transfer->num_iso_packets);
            }
        }
        break;
    case LIBUSB_TRANSFER_CANCELLED:
    case LIBUSB_TRANSFER_ERROR:
    case LIBUSB_TRANSFER_NO_DEVICE: {
        LOG_ERROR("transfer_callback status {} ", transfer->status);
        int i;
        for(i=0; i < USB_NUM_TRANSFER_BUFS; i++) {
            if(context->transfers[i] == transfer) {
                LOG_DEBUG("Freeing transfer {} (0x{:p})", i, (void *)transfer);
                free(transfer->buffer);
                libusb_free_transfer(transfer);
                context->transfers[i] = NULL;
                context->transfer_bufs[i] = NULL;
                break;
            }
        }
        if(i == USB_NUM_TRANSFER_BUFS ) {
            LOG_DEBUG("transfer 0x{:p} not found; not freeing!", (void*)transfer);
        }
        resubmit = 0;
        break;
    }
    case LIBUSB_TRANSFER_TIMED_OUT:
    case LIBUSB_TRANSFER_STALL:
    case LIBUSB_TRANSFER_OVERFLOW:
        LOG_ERROR("retrying transfer, status = {}", transfer->status);
        break;
    }

    if ( resubmit ) {
        if (context->async_usb_device->event_thread_running_) {
            int libusbRet = libusb_submit_transfer(transfer);
            if (libusbRet < 0){
                LOG_ERROR("libusb_submit_transfer failed, status = {}", transfer->status);
                //TODO 释放transfer
            }
        } else {
            LOG_ERROR("event_thread has exit");
            //TODO 释放transfer
        }
    }
}

void AsyncUSBDevice::HandleEvents() {
    timeval tv{0, 100000}; // 100ms timeout
    
    while (event_thread_running_) {
        int ret = libusb_handle_events_timeout_completed(usb_ctx_, &tv, nullptr);
        if (ret < 0 && ret != LIBUSB_ERROR_INTERRUPTED) {
            std::cerr << "Error handling events: " << libusb_error_name(ret) << std::endl;
            break;
        }
    }
}